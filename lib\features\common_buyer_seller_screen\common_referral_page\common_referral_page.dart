import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'dart:convert';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_how_swadesic.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_page_bloc.dart';
import 'package:swadesic/features/common_buyer_seller_screen/common_referral_page/common_referral_your_referral_makes.dart';
import 'package:swadesic/features/support/support_screen.dart';
import 'package:swadesic/features/buyers/messaging/new_messaging_chat_screen.dart';
import 'package:swadesic/features/common_buyer_seller_screen/app_web_view/app_web_view.dart';
import 'package:swadesic/model/invite_reward_info/invite_reward_info_response.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_methods.dart';
import 'package:swadesic/util/common_widgets.dart';
import 'package:url_launcher/url_launcher.dart';

class CommonReferralPage extends StatefulWidget {
  const CommonReferralPage({super.key});

  @override
  State<CommonReferralPage> createState() => _CommonReferralPageState();
}

class _CommonReferralPageState extends State<CommonReferralPage> with TickerProviderStateMixin {

  //region Bloc
  late CommonReferralPageBloc commonReferralPageBloc;
  //endregion

  //region Tab Controller
  late TabController tabController;
  //endregion

  //region Help Links Data
  Map<String, dynamic> helpLinksData = {};
  //endregion

  //region Init
  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    commonReferralPageBloc = CommonReferralPageBloc(context);
    commonReferralPageBloc.init();
    _loadHelpLinksData();
    super.initState();
  }
  //endregion

  //region Load Help Links Data
  void _loadHelpLinksData() async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;
      final helpLinksJson = remoteConfig.getString('app_help_links');
      if (helpLinksJson.isNotEmpty) {
        setState(() {
          helpLinksData = json.decode(helpLinksJson);
        });
      }
    } catch (e) {
      print('Error loading help links: $e');
    }
  }
  //endregion

  //region Dispose
  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }
  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      appBar: appBar(),
      body: Column(
        children: [
          // Tab Bar
          Container(
            color: AppColors.appWhite,
            child: TabBar(
              controller: tabController,
              indicator: const UnderlineTabIndicator(
                borderSide: BorderSide(
                  color: AppColors.appBlack,
                  width: 2.0,
                ),
              ),
              labelColor: AppColors.appBlack,
              unselectedLabelColor: AppColors.borderColor1,
              labelStyle: AppTextStyle.access1(textColor: AppColors.appBlack),
              unselectedLabelStyle: AppTextStyle.access1(textColor: AppColors.borderColor1),
              tabs: const [
                Tab(text: "Invite"),
                Tab(text: "Help"),
              ],
            ),
          ),
          // Tab Bar View
          Expanded(
            child: TabBarView(
              controller: tabController,
              children: [
                // Invite Tab (existing content)
                inviteTabContent(),
                // Help Tab (new content)
                helpTabContent(),
              ],
            ),
          ),
        ],
      ),
    );
  }


  //region Appbar
  AppBar appBar() {
    return AppCommonWidgets.mainAppBar(
      context: context,
      isCustomTitle: false,
      title:"App referral" ,
      isDefaultMenuVisible: true,
      isMembershipVisible: false,
      isCartVisible: false,
    );
  }

//endregion
  Widget challengeSection() {

    return Container(
      width: MediaQuery.of(context).size.width,
      margin: const EdgeInsets.symmetric(horizontal: 0),
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      decoration: const BoxDecoration(
        color: AppColors.appBlack,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                "⚡",
                style: TextStyle(fontSize: 20),
              ),
              const SizedBox(width: 5),
              Text(
                "The Challenge:",
                style: AppTextStyle.introSlideTitle(textColor: Colors.white)
                    .copyWith(fontSize: 22, fontFamily: AppConstants.leagueSemiBold),
              ),
            ],
          ),
          Text(
            "Bring 3 Buyers. Bring 1 Seller,\nToday. Change the Game.",
            style: AppTextStyle.introSlideTitle(textColor: Colors.white)
                .copyWith(fontSize: 22, fontFamily: AppConstants.leagueSemiBold),
          ),
        ],
      ),
    );
  }




  //region Invite Tab Content
  Widget inviteTabContent(){
    return StreamBuilder<CommonReferralPageState>(
      stream: commonReferralPageBloc.commonReferralPageStateCtrl.stream,
      initialData:CommonReferralPageState.Loading ,
      builder: (context, snapshot) {
        //Success
        if(snapshot.data == CommonReferralPageState.Success){
          return SingleChildScrollView(
            child: Column(
              children: [
                indiaFlag(),
                // yourReferralMakesADifference(showTextAndBorder: false),
                join(),
                // const SizedBox(height: 30),
                // challengeSection(),
                // const SizedBox(height: 30),
                // rewardSection(),
                // const SizedBox(height: 30),
                // howSwadesic(),
                yourReferralMakesADifference(showTextAndBorder: true),
                const SizedBox(height: 30),
                footer(),

              ],
            ),
          );
        }
        //Loading
        if(snapshot.data == CommonReferralPageState.Loading){
          return AppCommonWidgets.appCircularProgress();
        }
        return const SizedBox();
      }
    );
  }
  //endregion


//region Inda flag
Widget indiaFlag(){
    return Stack(
      children: [
        Container(
          width: 375,
          height: 235,
          margin: const EdgeInsets.only(left: 0, right: 0, top: 60, bottom: 0),
          child: Image.asset(AppImages.missionPageImage,fit: BoxFit.fill),
        ),
        Positioned.fill(
            child: Container(
              margin: const EdgeInsets.only(bottom: 5),
                alignment: Alignment.bottomCenter,
                // child: Text("Build Atmanirbhar Bharat helping  Swadeshi businesses!",style: AppTextStyle.introSlideTitle(textColor: AppColors.appWhite).copyWith(fontSize: 13,fontFamily: AppConstants.leagueSemiBold),)))
                )
        )
      ],
    );
}
//endregion


  //region Join the swadesic
  Widget bulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15, right: 5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10, right: 10, left: 10),
            child: Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: AppColors.appBlack,
                shape: BoxShape.circle,
              ),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
            ),
          ),
        ],
      ),
    );
  }

  Widget join(){
    return Container(
      margin: EdgeInsets.only(top: 20,bottom: 10,right: 15,left: 15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Help us accelerate Swadeshi Mission",
            style: AppTextStyle.exHeading2(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),

          CupertinoButton(
            padding: EdgeInsets.zero,
            onPressed: () {
              if(CommonMethods().isStaticUser()){
                CommonMethods().goToSignUpFlow();
                return ;
              }
              CommonMethods.share(
                  "${AppConstants.appData.isUserView! ? AppStrings.userInviteeMessage : AppStrings.storeInviteeMessage}\n${AppConstants.domainName}?ref=${commonReferralPageBloc.inviteCode}");
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              // margin: const EdgeInsets.only(left: 10, right: 10, top: 0),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: AppColors.brandBlack,
                  borderRadius:
                      BorderRadius.circular(MediaQuery.of(context).size.width * 0.5)),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Image.asset(
                  //   AppImages.basketWithStar,
                  //   height: 40,
                  //   width: 40,
                  // ),
                  // const SizedBox(width: 10),
                  Text(
                    "Invite with your code",
                    style: AppTextStyle.access1(textColor: AppColors.appWhite),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),

          Text(
            "Bharat can only grow strong if money stays within. We lose ₹2.2 lakh crore every year through foreign tech, companies, and supply chains — that's 350 Chandrayaan-3 missions gone.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),
          Text(
            "We can’t negotiate in critical sectors like defense or energy. But in everything else, we can choose Swadeshi.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),
          Text(
            "Swadesic is a tech partner for Indian small businesses — giving them a free store to run business, build community, and offer a professional shopping experience while simplifying their operations.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),
          Text(
            "No percentage based commissions.Stores are owned via phone number — just like a digital asset. Perfect for businesses running on WhatsApp or Instagram.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),
          Text(
            "For buyers, it's a safe, direct way to shop from brands they love — while discovering local creators and like-minded communities.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 20),

          // bulletPoint(
          //   "We can’t negotiate critical goods like Defense, Energy and Technology sectors."
          // ),
          // bulletPoint(
          //   "At least we can make a strong Swadeshi Economy in other sectors. Swadesic is an initiative to support Indian business ecosystem by being a technology partner & accelerator."
          // ),
          // bulletPoint(
          //   "For someone who runs a business, Swadesic provides a free store to run their operations. Unlike in other platforms, swadesic store is a digital asset that gets registered to user’s phone number."
          // ),
          // bulletPoint(
          //   "With Swadesic stores, businesses can build business & community at one place. Offer a professional shopping & an engaging brand experience to their customers while simplifying business operations."
          // ),
          // bulletPoint(
          //   "For consumers, Swadesic opens up a safe place to shop directly from brands they love on social media and localities. Consumers can know more about the brands, people behind it and others who support same brands and interests."
          // ),
          // const SizedBox(height:20)
        ],
      ),
    );
  }
  //endregion


//region Your referral makes a difference
Widget yourReferralMakesADifference({bool showTextAndBorder = false}){
    return  CommonReferralYourReferralMakes(commonReferralPageBloc: commonReferralPageBloc,showTextAndBorder: showTextAndBorder);
}
//endregion

//region How swadesic
Widget howSwadesic(){
    return  CommonReferralHowSwadesic(commonReferralPageBloc: commonReferralPageBloc);
}
//endregion

  Widget rewardSection() {
    return Container(
      margin: EdgeInsets.only(top: 20, bottom: 10, right: 20, left: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Because if 10 Crore Indians brought just one more, we'd flip the entire economy in months.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
          ),
          SizedBox(height: 20),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "💰 Your Reward? A stronger Bharat. A self-reliant economy. Oh, and exclusive perks just for being part of the revolution.",
                  style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

//region Footer
Widget footer(){
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Be part of India's growth story with Swadesic. Be part of the Swadeshi revolution.",
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack)
          ),
          const SizedBox(height: 30),
          Text(
            '"Bharat is calling for her noblest sons and daughters. Will you answer the call?"',
            style: AppTextStyle.settingHeading1(textColor: AppColors.appBlack).copyWith(fontStyle: FontStyle.italic)
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                "— Inspired by Netaji Subhas Chandra Bose",
                style: AppTextStyle.smallText(textColor: AppColors.appBlack)
              ),
            ],
          ),
        ],
      ),
    );
  }
//endregion




}
